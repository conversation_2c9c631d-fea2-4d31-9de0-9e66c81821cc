import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';

const SystemsStateLogger: React.FC = () => {
  const systemsState = useSelector((state: any) => state.systems);
  
  useEffect(() => {
    console.log('=== SYSTEMS REDUX STATE ===');
    console.log('Full systems state:', systemsState);
    
    if (systemsState?.systems && systemsState.systems.length > 0) {
      console.log('Systems array:', systemsState.systems);
      
      systemsState.systems.forEach((system: any, index: number) => {
        console.log(`System ${index}:`, system);
        
        if (system.config && Array.isArray(system.config)) {
          system.config.forEach((config: any, configIndex: number) => {
            console.log(`System ${index} - Config ${configIndex}:`, config);
            
            // Look for quality parameter ranges in the config
            if (config.configurations) {
              console.log(`System ${index} - Config ${configIndex} - Configurations:`, config.configurations);
            }
            
            // Look for target variables with ranges
            if (config.targetVariables) {
              console.log(`System ${index} - Config ${configIndex} - Target Variables:`, config.targetVariables);
            }
            
            // Look for any quality-related data
            Object.keys(config).forEach(key => {
              if (key.toLowerCase().includes('quality') || key.toLowerCase().includes('target') || key.toLowerCase().includes('range')) {
                console.log(`System ${index} - Config ${configIndex} - ${key}:`, config[key]);
              }
            });
          });
        }
      });
    }
    
    console.log('=== END SYSTEMS REDUX STATE ===');
  }, [systemsState]);

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      background: 'rgba(0,0,0,0.8)', 
      color: 'white', 
      padding: '10px', 
      borderRadius: '5px',
      fontSize: '12px',
      zIndex: 9999,
      maxWidth: '300px'
    }}>
      <div>Systems State Logger Active</div>
      <div>Check console for detailed logs</div>
      <div>Systems count: {systemsState?.systems?.length || 0}</div>
    </div>
  );
};

export default SystemsStateLogger;
